from gliner import GLiNER

model = GLiNER.from_pretrained("urchade/gliner_medium-v2.1")
text = "<PERSON><PERSON><PERSON> se <PERSON> v ten den vrátil domů z oběda, <PERSON><PERSON><PERSON><PERSON>, že mu na stole leží do<PERSON> od <PERSON>, s.r.o., ve kter<PERSON><PERSON> se p<PERSON><PERSON><PERSON>, že už od 1. října 2023 může nastoupit jako vedoucí obchodu na nové pobočce ve Světlé nad Sázavou."

# Common NER categories
labels = ["person", "organization", "location", "date"]

entities = model.predict_entities(text, labels)

for entity in entities:
    print(entity["text"], "-", entity["label"])