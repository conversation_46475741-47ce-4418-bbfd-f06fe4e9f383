from gliner import GLiNER

model = GLiNER.from_pretrained("knowledgator/gliner-x-large")
text = "<PERSON><PERSON><PERSON> se Jan <PERSON>ák v ten den vrátil domů z oběda, <PERSON><PERSON><PERSON><PERSON>, že mu na stole leží do<PERSON> od <PERSON>, s.r.o., ve kter<PERSON><PERSON> se p<PERSON><PERSON>, že už od 1. října 2023 může nastoupit jako vedoucí obchodu na nové pobočce ve Světlé nad Sázavou."

# Common NER categories
labels = ["person", "organization", "location", "date"]

entities = model.predict_entities(text, labels)

for entity in entities:
    print(entity["text"], "-", entity["label"])