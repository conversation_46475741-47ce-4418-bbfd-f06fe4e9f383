{"class_token_index": 250100, "decoder_mode": null, "dropout": 0.3, "embed_ent_token": true, "encoder_config": {"_attn_implementation_autoset": true, "_name_or_path": "google/mt5-large", "add_cross_attention": false, "architectures": ["MT5ForConditionalGeneration"], "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "classifier_dropout": 0.0, "cross_attention_hidden_size": null, "d_ff": 2816, "d_kv": 64, "d_model": 1024, "decoder_start_token_id": 0, "dense_act_fn": "gelu_new", "diversity_penalty": 0.0, "do_sample": false, "dropout_rate": 0.1, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 1, "exponential_decay_length_penalty": null, "feed_forward_proj": "gated-gelu", "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_factor": 1.0, "is_decoder": false, "is_encoder_decoder": true, "is_gated_act": true, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_epsilon": 1e-06, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "model_type": "mt5", "no_repeat_ngram_size": 0, "num_beam_groups": 1, "num_beams": 1, "num_decoder_layers": 24, "num_heads": 16, "num_layers": 24, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_past": true, "output_scores": false, "pad_token_id": 0, "prefix": null, "problem_type": null, "pruned_heads": {}, "relative_attention_max_distance": 128, "relative_attention_num_buckets": 32, "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": false, "tokenizer_class": "T5Tokenizer", "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "typical_p": 1.0, "use_bfloat16": false, "use_cache": true, "vocab_size": 250102}, "ent_token": "<<ENT>>", "eval_every": 10000, "fine_tune": true, "freeze_token_rep": false, "full_decoder_context": true, "fuse_layers": false, "has_rnn": true, "hidden_size": 1024, "label_smoothing": 0, "labels_decoder": null, "labels_decoder_config": null, "labels_encoder": null, "labels_encoder_config": null, "log_dir": "models/", "loss_alpha": 0.75, "loss_gamma": 0, "loss_reduction": "sum", "lr_encoder": "1e-5", "lr_others": "3e-5", "max_grad_norm": 10.0, "max_len": 1024, "max_neg_type_ratio": 1, "max_types": 30, "max_width": 12, "model_name": "google/mt5-large", "model_type": "gliner", "name": "span level gliner", "num_post_fusion_layers": 1, "num_steps": 80000, "post_fusion_schema": "", "prev_path": null, "random_drop": true, "root_dir": "gliner_logs", "save_total_limit": 3, "scheduler_type": "cosine", "sep_token": "<<SEP>>", "shuffle_types": true, "size_sup": -1, "span_mode": "markerV0", "subtoken_pooling": "first", "train_batch_size": 4, "train_data": "data/multilingual_data.json", "transformers_version": "4.51.0", "val_data_dir": "none", "vocab_size": 250102, "warmup_ratio": 0.05, "weight_decay_encoder": 0.1, "weight_decay_other": 0.01, "words_splitter_type": "stanza"}