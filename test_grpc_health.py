#!/usr/bin/env python3
"""
Skript pro testování gRPC health check služby na serveru.
Testuje dostupnost služby pro úpravu obrazu před OCR.
"""

import grpc
from grpc_health.v1 import health_pb2
from grpc_health.v1 import health_pb2_grpc
import sys
import time

def test_grpc_health(server_address, service_name="", timeout=10):
    """
    Testuje health check gRPC služby.
    
    Args:
        server_address (str): Adresa serveru ve formátu "host:port"
        service_name (str): Název služby k testování (prázdný string pro celkový health check)
        timeout (int): Timeout v sekundách
    
    Returns:
        tuple: (success: bool, status: str, message: str)
    """
    try:
        print(f"Připojuji se k gRPC serveru: {server_address}")
        print(f"Testovaná služba: '{service_name}' (pr<PERSON>z<PERSON><PERSON> = celkový health check)")
        print(f"Timeout: {timeout} sekund")
        print("-" * 50)
        
        # Vyt<PERSON>ření kanálu s timeoutem
        channel = grpc.insecure_channel(server_address)
        
        # Čekání na připojení
        try:
            grpc.channel_ready_future(channel).result(timeout=timeout)
            print("✓ Připojení k serveru úspěšné")
        except grpc.FutureTimeoutError:
            return False, "TIMEOUT", f"Nepodařilo se připojit k serveru během {timeout} sekund"
        
        # Vytvoření stub pro health check
        health_stub = health_pb2_grpc.HealthStub(channel)
        
        # Vytvoření požadavku
        request = health_pb2.HealthCheckRequest(service=service_name)
        
        # Provedení health check
        print("Provádím health check...")
        response = health_stub.Check(request, timeout=timeout)
        
        # Interpretace odpovědi
        status_map = {
            health_pb2.HealthCheckResponse.UNKNOWN: "UNKNOWN",
            health_pb2.HealthCheckResponse.SERVING: "SERVING", 
            health_pb2.HealthCheckResponse.NOT_SERVING: "NOT_SERVING",
            health_pb2.HealthCheckResponse.SERVICE_UNKNOWN: "SERVICE_UNKNOWN"
        }
        
        status_str = status_map.get(response.status, f"NEZNÁMÝ_STATUS_{response.status}")
        
        print(f"✓ Health check dokončen")
        print(f"Status: {status_str}")
        
        # Uzavření kanálu
        channel.close()
        
        success = response.status == health_pb2.HealthCheckResponse.SERVING
        message = f"Služba je {'dostupná' if success else 'nedostupná'} (status: {status_str})"
        
        return success, status_str, message
        
    except grpc.RpcError as e:
        error_msg = f"gRPC chyba: {e.code()} - {e.details()}"
        print(f"✗ {error_msg}")
        return False, "RPC_ERROR", error_msg
        
    except Exception as e:
        error_msg = f"Neočekávaná chyba: {str(e)}"
        print(f"✗ {error_msg}")
        return False, "ERROR", error_msg

def main():
    """Hlavní funkce pro testování služby."""
    server_address = "*************:50051"  # Výchozí gRPC port
    
    print("=== Test gRPC Health Check Služby ===")
    print(f"Server: {server_address}")
    print()
    
    # Test 1: Celkový health check (prázdný service name)
    print("1. Testování celkového health check...")
    success1, status1, message1 = test_grpc_health(server_address, "", timeout=10)
    print(f"Výsledek: {message1}")
    print()
    
    # Test 2: Pokus o konkrétní službu (pokud je známá)
    print("2. Testování konkrétní služby...")
    # Můžeme zkusit různé názvy služeb
    service_names = ["ImagePreprocessor", "OCRPreprocessor", "image_processor", ""]
    
    for service_name in service_names:
        print(f"   Testování služby: '{service_name}'")
        success, status, message = test_grpc_health(server_address, service_name, timeout=5)
        print(f"   -> {status}: {message}")
        if success:
            print(f"   ✓ Služba '{service_name}' je dostupná!")
            break
    
    print()
    print("=== Shrnutí ===")
    if success1:
        print("✓ Server je dostupný a zdravý")
        print("✓ Můžeme pokračovat s implementací gRPC klienta")
    else:
        print("✗ Server není dostupný nebo má problémy")
        print("✗ Zkontrolujte síťové připojení a stav serveru")
        
        # Pokus o různé porty
        print("\nZkouším alternativní porty...")
        for port in [50051, 8080, 9090, 50052]:
            alt_address = f"*************:{port}"
            print(f"Testování portu {port}...")
            success, status, message = test_grpc_health(alt_address, "", timeout=3)
            if success:
                print(f"✓ Služba nalezena na portu {port}!")
                break
            else:
                print(f"✗ Port {port}: {status}")

if __name__ == "__main__":
    main()
