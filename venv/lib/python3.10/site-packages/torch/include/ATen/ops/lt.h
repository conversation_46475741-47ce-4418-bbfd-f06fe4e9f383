#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/lt_ops.h>

namespace at {


// aten::lt.Scalar_out(Tensor self, Scalar other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & lt_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other) {
    return at::_ops::lt_Scalar_out::call(self, other, out);
}
// aten::lt.<PERSON><PERSON><PERSON>_out(Tensor self, <PERSON>alar other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & lt_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out) {
    return at::_ops::lt_Scalar_out::call(self, other, out);
}

// aten::lt.Scalar(Tensor self, Scalar other) -> Tensor
inline at::Tensor lt(const at::Tensor & self, const at::Scalar & other) {
    return at::_ops::lt_Scalar::call(self, other);
}

// aten::lt.Tensor_out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & lt_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::lt_Tensor_out::call(self, other, out);
}
// aten::lt.Tensor_out(Tensor self, Tensor other, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & lt_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out) {
    return at::_ops::lt_Tensor_out::call(self, other, out);
}

// aten::lt.Tensor(Tensor self, Tensor other) -> Tensor
inline at::Tensor lt(const at::Tensor & self, const at::Tensor & other) {
    return at::_ops::lt_Tensor::call(self, other);
}

}
